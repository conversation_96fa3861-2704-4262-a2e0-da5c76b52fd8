{"version": 2, "dgSpecHash": "H8KLZklj2kk=", "success": true, "projectFilePath": "/Users/<USER>/Documents/Test Projects/Desktop App/DatabaseBackupApp.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/avalonia/11.0.10/avalonia.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.angle.windows.natives/2.1.0.2023020321/avalonia.angle.windows.natives.2.1.0.2023020321.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.buildservices/0.0.29/avalonia.buildservices.0.0.29.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.controls.colorpicker/11.0.10/avalonia.controls.colorpicker.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.controls.datagrid/11.0.10/avalonia.controls.datagrid.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.desktop/11.0.10/avalonia.desktop.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.diagnostics/11.0.10/avalonia.diagnostics.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.fonts.inter/11.0.10/avalonia.fonts.inter.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.freedesktop/11.0.10/avalonia.freedesktop.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.native/11.0.10/avalonia.native.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.remote.protocol/11.0.10/avalonia.remote.protocol.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.skia/11.0.10/avalonia.skia.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.themes.fluent/11.0.10/avalonia.themes.fluent.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.themes.simple/11.0.10/avalonia.themes.simple.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.win32/11.0.10/avalonia.win32.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.x11/11.0.10/avalonia.x11.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/azure.core/1.25.0/azure.core.1.25.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/azure.identity/1.7.0/azure.identity.1.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/bouncycastle.cryptography/2.2.1/bouncycastle.cryptography.2.2.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/google.protobuf/3.21.9/google.protobuf.3.21.9.nupkg.sha512", "/Users/<USER>/.nuget/packages/harfbuzzsharp/7.3.0/harfbuzzsharp.7.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/harfbuzzsharp.nativeassets.linux/7.3.0/harfbuzzsharp.nativeassets.linux.7.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/harfbuzzsharp.nativeassets.macos/7.3.0/harfbuzzsharp.nativeassets.macos.7.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/harfbuzzsharp.nativeassets.webassembly/7.3.0/harfbuzzsharp.nativeassets.webassembly.7.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/harfbuzzsharp.nativeassets.win32/7.3.0/harfbuzzsharp.nativeassets.win32.7.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/k4os.compression.lz4/1.3.5/k4os.compression.lz4.1.3.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/k4os.compression.lz4.streams/1.3.5/k4os.compression.lz4.streams.1.3.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/k4os.hash.xxhash/1.0.8/k4os.hash.xxhash.1.0.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/microcom.runtime/0.11.0/microcom.runtime.0.11.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.bcl.asyncinterfaces/1.1.1/microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codeanalysis.analyzers/3.0.0/microsoft.codeanalysis.analyzers.3.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codeanalysis.common/3.8.0/microsoft.codeanalysis.common.3.8.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codeanalysis.csharp/3.8.0/microsoft.codeanalysis.csharp.3.8.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codeanalysis.csharp.scripting/3.8.0/microsoft.codeanalysis.csharp.scripting.3.8.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codeanalysis.scripting.common/3.8.0/microsoft.codeanalysis.scripting.common.3.8.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.csharp/4.5.0/microsoft.csharp.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.data.sqlclient/5.1.2/microsoft.data.sqlclient.5.1.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.data.sqlclient.sni.runtime/5.1.1/microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/8.0.0/microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/8.0.0/microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identity.client/4.47.2/microsoft.identity.client.4.47.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identity.client.extensions.msal/2.19.3/microsoft.identity.client.extensions.msal.2.19.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.abstractions/6.24.0/microsoft.identitymodel.abstractions.6.24.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.jsonwebtokens/6.24.0/microsoft.identitymodel.jsonwebtokens.6.24.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.logging/6.24.0/microsoft.identitymodel.logging.6.24.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.protocols/6.24.0/microsoft.identitymodel.protocols.6.24.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.protocols.openidconnect/6.24.0/microsoft.identitymodel.protocols.openidconnect.6.24.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.tokens/6.24.0/microsoft.identitymodel.tokens.6.24.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.platforms/1.1.0/microsoft.netcore.platforms.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.targets/1.1.0/microsoft.netcore.targets.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.sqlserver.server/1.0.0/microsoft.sqlserver.server.1.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.systemevents/6.0.0/microsoft.win32.systemevents.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/mysql.data/8.2.0/mysql.data.8.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/npgsql/8.0.1/npgsql.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/skiasharp/2.88.7/skiasharp.2.88.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/skiasharp.nativeassets.linux/2.88.7/skiasharp.nativeassets.linux.2.88.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/skiasharp.nativeassets.macos/2.88.7/skiasharp.nativeassets.macos.2.88.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/skiasharp.nativeassets.webassembly/2.88.7/skiasharp.nativeassets.webassembly.2.88.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/skiasharp.nativeassets.win32/2.88.7/skiasharp.nativeassets.win32.2.88.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.buffers/4.5.1/system.buffers.4.5.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections.immutable/5.0.0/system.collections.immutable.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.componentmodel.annotations/4.5.0/system.componentmodel.annotations.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.configuration.configurationmanager/6.0.1/system.configuration.configurationmanager.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/7.0.2/system.diagnostics.diagnosticsource.7.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.drawing.common/6.0.0/system.drawing.common.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.formats.asn1/5.0.0/system.formats.asn1.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.identitymodel.tokens.jwt/6.24.0/system.identitymodel.tokens.jwt.6.24.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io/4.3.0/system.io.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.filesystem.accesscontrol/5.0.0/system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.pipelines/6.0.3/system.io.pipelines.6.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory/4.5.4/system.memory.4.5.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory.data/1.0.2/system.memory.data.1.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.numerics.vectors/4.5.0/system.numerics.vectors.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection/4.3.0/system.reflection.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.metadata/5.0.0/system.reflection.metadata.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.primitives/4.3.0/system.reflection.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime/4.3.0/system.runtime.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.caching/6.0.0/system.runtime.caching.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.compilerservices.unsafe/6.0.0/system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.loader/4.3.0/system.runtime.loader.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.accesscontrol/6.0.0/system.security.accesscontrol.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.cng/5.0.0/system.security.cryptography.cng.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.protecteddata/6.0.0/system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.permissions/6.0.0/system.security.permissions.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.principal.windows/5.0.0/system.security.principal.windows.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding/4.3.0/system.text.encoding.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding.codepages/6.0.0/system.text.encoding.codepages.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encodings.web/7.0.0/system.text.encodings.web.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.json/7.0.1/system.text.json.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks/4.3.0/system.threading.tasks.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks.extensions/4.5.4/system.threading.tasks.extensions.4.5.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.windows.extensions/6.0.0/system.windows.extensions.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/tmds.dbus.protocol/0.15.0/tmds.dbus.protocol.0.15.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/zstdsharp.port/0.7.1/zstdsharp.port.0.7.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.18/microsoft.netcore.app.ref.8.0.18.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/8.0.18/microsoft.aspnetcore.app.ref.8.0.18.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.host.osx-x64/8.0.18/microsoft.netcore.app.host.osx-x64.8.0.18.nupkg.sha512"], "logs": [{"code": "NU1903", "level": "Warning", "message": "Package 'Microsoft.Data.SqlClient' 5.1.2 has a known high severity vulnerability, https://github.com/advisories/GHSA-98g6-xh36-x2p7", "projectPath": "/Users/<USER>/Documents/Test Projects/Desktop App/DatabaseBackupApp.csproj", "warningLevel": 1, "filePath": "/Users/<USER>/Documents/Test Projects/Desktop App/DatabaseBackupApp.csproj", "libraryId": "Microsoft.Data.SqlClient", "targetGraphs": ["net8.0"]}, {"code": "NU1903", "level": "Warning", "message": "Package 'Npgsql' 8.0.1 has a known high severity vulnerability, https://github.com/advisories/GHSA-x9vc-6hfv-hg8c", "projectPath": "/Users/<USER>/Documents/Test Projects/Desktop App/DatabaseBackupApp.csproj", "warningLevel": 1, "filePath": "/Users/<USER>/Documents/Test Projects/Desktop App/DatabaseBackupApp.csproj", "libraryId": "Npgsql", "targetGraphs": ["net8.0"]}]}