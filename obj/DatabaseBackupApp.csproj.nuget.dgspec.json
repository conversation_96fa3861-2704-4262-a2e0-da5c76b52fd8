{"format": 1, "restore": {"/Users/<USER>/Documents/Test Projects/Desktop App/DatabaseBackupApp.csproj": {}}, "projects": {"/Users/<USER>/Documents/Test Projects/Desktop App/DatabaseBackupApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Test Projects/Desktop App/DatabaseBackupApp.csproj", "projectName": "DatabaseBackupApp", "projectPath": "/Users/<USER>/Documents/Test Projects/Desktop App/DatabaseBackupApp.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Test Projects/Desktop App/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Avalonia": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Desktop": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Fonts.Inter": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Themes.Fluent": {"target": "Package", "version": "[11.0.10, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.2, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.7, )", "autoReferenced": true}, "MySql.Data": {"target": "Package", "version": "[8.2.0, )"}, "Npgsql": {"target": "Package", "version": "[8.0.1, )"}, "System.IO.FileSystem.AccessControl": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.osx-x64", "version": "[9.0.7, 9.0.7]"}, {"name": "Microsoft.NETCore.App.Runtime.osx-x64", "version": "[9.0.7, 9.0.7]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.303/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"osx-x64": {"#import": []}}}}}