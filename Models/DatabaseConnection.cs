using System.ComponentModel;

namespace DatabaseBackupApp.Models
{
    public enum DatabaseType
    {
        SqlServer,
        MySQL,
        PostgreSQL
    }

    public enum BackupDestination
    {
        Local,
        SMB
    }

    public class DatabaseConnection : INotifyPropertyChanged
    {
        private string _server = string.Empty;
        private string _database = string.Empty;
        private string _username = string.Empty;
        private string _password = string.Empty;
        private int _port;
        private DatabaseType _databaseType = DatabaseType.SqlServer;
        private bool _useWindowsAuth;

        public string Server
        {
            get => _server;
            set
            {
                _server = value;
                OnPropertyChanged(nameof(Server));
            }
        }

        public string Database
        {
            get => _database;
            set
            {
                _database = value;
                OnPropertyChanged(nameof(Database));
            }
        }

        public string Username
        {
            get => _username;
            set
            {
                _username = value;
                OnPropertyChanged(nameof(Username));
            }
        }

        public string Password
        {
            get => _password;
            set
            {
                _password = value;
                OnPropertyChanged(nameof(Password));
            }
        }

        public int Port
        {
            get => _port;
            set
            {
                _port = value;
                OnPropertyChanged(nameof(Port));
            }
        }

        public DatabaseType DatabaseType
        {
            get => _databaseType;
            set
            {
                _databaseType = value;
                SetDefaultPort();
                OnPropertyChanged(nameof(DatabaseType));
            }
        }

        public bool UseWindowsAuth
        {
            get => _useWindowsAuth;
            set
            {
                _useWindowsAuth = value;
                OnPropertyChanged(nameof(UseWindowsAuth));
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void SetDefaultPort()
        {
            Port = DatabaseType switch
            {
                DatabaseType.SqlServer => 1433,
                DatabaseType.MySQL => 3306,
                DatabaseType.PostgreSQL => 5432,
                _ => 1433
            };
        }

        public string GetConnectionString()
        {
            return DatabaseType switch
            {
                DatabaseType.SqlServer => GetSqlServerConnectionString(),
                DatabaseType.MySQL => GetMySqlConnectionString(),
                DatabaseType.PostgreSQL => GetPostgreSqlConnectionString(),
                _ => throw new NotSupportedException($"Database type {DatabaseType} is not supported")
            };
        }

        private string GetSqlServerConnectionString()
        {
            if (UseWindowsAuth)
            {
                return $"Server={Server},{Port};Database={Database};Integrated Security=true;TrustServerCertificate=true;";
            }
            return $"Server={Server},{Port};Database={Database};User Id={Username};Password={Password};TrustServerCertificate=true;";
        }

        private string GetMySqlConnectionString()
        {
            return $"Server={Server};Port={Port};Database={Database};Uid={Username};Pwd={Password};";
        }

        private string GetPostgreSqlConnectionString()
        {
            return $"Host={Server};Port={Port};Database={Database};Username={Username};Password={Password};";
        }
    }

    public class BackupSettings : INotifyPropertyChanged
    {
        private string _localPath = string.Empty;
        private string _smbPath = string.Empty;
        private string _smbUsername = string.Empty;
        private string _smbPassword = string.Empty;
        private BackupDestination _destination = BackupDestination.Local;

        public string LocalPath
        {
            get => _localPath;
            set
            {
                _localPath = value;
                OnPropertyChanged(nameof(LocalPath));
            }
        }

        public string SmbPath
        {
            get => _smbPath;
            set
            {
                _smbPath = value;
                OnPropertyChanged(nameof(SmbPath));
            }
        }

        public string SmbUsername
        {
            get => _smbUsername;
            set
            {
                _smbUsername = value;
                OnPropertyChanged(nameof(SmbUsername));
            }
        }

        public string SmbPassword
        {
            get => _smbPassword;
            set
            {
                _smbPassword = value;
                OnPropertyChanged(nameof(SmbPassword));
            }
        }

        public BackupDestination Destination
        {
            get => _destination;
            set
            {
                _destination = value;
                OnPropertyChanged(nameof(Destination));
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
