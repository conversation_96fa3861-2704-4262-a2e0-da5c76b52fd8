<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:DatabaseBackupApp.ViewModels"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="900" d:DesignHeight="700"
        x:Class="DatabaseBackupApp.Views.MainWindow"
        x:DataType="vm:MainWindowViewModel"
        Icon="/Assets/avalonia-logo.ico"
        Title="Database Backup & Restore Tool"
        Width="900" Height="700">

    <Design.DataContext>
        <vm:MainWindowViewModel/>
    </Design.DataContext>

    <ScrollViewer>
        <StackPanel Margin="20" Spacing="20">
            
            <!-- Status Bar -->
            <Border Background="LightGray" Padding="10" CornerRadius="5">
                <TextBlock Text="{Binding StatusMessage}" FontWeight="Bold"/>
            </Border>

            <!-- Source Database Connection -->
            <Border BorderBrush="Gray" BorderThickness="1" Padding="15" CornerRadius="5">
                <StackPanel Spacing="10">
                    <TextBlock Text="Source Database Connection" FontSize="16" FontWeight="Bold"/>
                    
                    <Grid ColumnDefinitions="Auto,*,Auto,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto" Margin="0,10">
                        <Grid.Styles>
                            <Style Selector="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Margin" Value="0,0,10,0"/>
                            </Style>
                            <Style Selector="TextBox">
                                <Setter Property="Margin" Value="0,0,20,10"/>
                            </Style>
                            <Style Selector="ComboBox">
                                <Setter Property="Margin" Value="0,0,20,10"/>
                            </Style>
                        </Grid.Styles>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Database Type:"/>
                        <ComboBox Grid.Row="0" Grid.Column="1" ItemsSource="{Binding DatabaseTypes}" 
                                  SelectedItem="{Binding SourceConnection.DatabaseType}"/>

                        <TextBlock Grid.Row="0" Grid.Column="2" Text="Server:"/>
                        <TextBox Grid.Row="0" Grid.Column="3" Text="{Binding SourceConnection.Server}"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Port:"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding SourceConnection.Port}"/>

                        <TextBlock Grid.Row="1" Grid.Column="2" Text="Database:"/>
                        <TextBox Grid.Row="1" Grid.Column="3" Text="{Binding SourceConnection.Database}"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Username:"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding SourceConnection.Username}"/>

                        <TextBlock Grid.Row="2" Grid.Column="2" Text="Password:"/>
                        <TextBox Grid.Row="2" Grid.Column="3" Text="{Binding SourceConnection.Password}" 
                                 PasswordChar="*" RevealPassword="False"/>

                        <CheckBox Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" 
                                  Content="Use Windows Authentication" 
                                  IsChecked="{Binding SourceConnection.UseWindowsAuth}"/>
                    </Grid>

                    <Button Content="Test Connection" Command="{Binding TestSourceConnectionCommand}" 
                            HorizontalAlignment="Left" IsEnabled="{Binding !IsOperationInProgress}"/>
                </StackPanel>
            </Border>

            <!-- Target Database Connection -->
            <Border BorderBrush="Gray" BorderThickness="1" Padding="15" CornerRadius="5">
                <StackPanel Spacing="10">
                    <TextBlock Text="Target Database Connection" FontSize="16" FontWeight="Bold"/>
                    
                    <Grid ColumnDefinitions="Auto,*,Auto,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto" Margin="0,10">
                        <Grid.Styles>
                            <Style Selector="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Margin" Value="0,0,10,0"/>
                            </Style>
                            <Style Selector="TextBox">
                                <Setter Property="Margin" Value="0,0,20,10"/>
                            </Style>
                            <Style Selector="ComboBox">
                                <Setter Property="Margin" Value="0,0,20,10"/>
                            </Style>
                        </Grid.Styles>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Database Type:"/>
                        <ComboBox Grid.Row="0" Grid.Column="1" ItemsSource="{Binding DatabaseTypes}" 
                                  SelectedItem="{Binding TargetConnection.DatabaseType}"/>

                        <TextBlock Grid.Row="0" Grid.Column="2" Text="Server:"/>
                        <TextBox Grid.Row="0" Grid.Column="3" Text="{Binding TargetConnection.Server}"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Port:"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding TargetConnection.Port}"/>

                        <TextBlock Grid.Row="1" Grid.Column="2" Text="Database:"/>
                        <TextBox Grid.Row="1" Grid.Column="3" Text="{Binding TargetConnection.Database}"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Username:"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding TargetConnection.Username}"/>

                        <TextBlock Grid.Row="2" Grid.Column="2" Text="Password:"/>
                        <TextBox Grid.Row="2" Grid.Column="3" Text="{Binding TargetConnection.Password}" 
                                 PasswordChar="*" RevealPassword="False"/>

                        <CheckBox Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" 
                                  Content="Use Windows Authentication" 
                                  IsChecked="{Binding TargetConnection.UseWindowsAuth}"/>
                    </Grid>

                    <Button Content="Test Connection" Command="{Binding TestTargetConnectionCommand}" 
                            HorizontalAlignment="Left" IsEnabled="{Binding !IsOperationInProgress}"/>
                </StackPanel>
            </Border>

            <!-- Backup Settings -->
            <Border BorderBrush="Gray" BorderThickness="1" Padding="15" CornerRadius="5">
                <StackPanel Spacing="10">
                    <TextBlock Text="Backup Settings" FontSize="16" FontWeight="Bold"/>
                    
                    <Grid ColumnDefinitions="Auto,*,Auto" RowDefinitions="Auto,Auto,Auto,Auto" Margin="0,10">
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Destination:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <ComboBox Grid.Row="0" Grid.Column="1" ItemsSource="{Binding BackupDestinations}" 
                                  SelectedItem="{Binding BackupSettings.Destination}" Margin="0,0,20,10"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Local Path:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding BackupSettings.LocalPath}" Margin="0,0,10,10"/>
                        <Button Grid.Row="1" Grid.Column="2" Content="Browse" Command="{Binding BrowseLocalPathCommand}"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="SMB Path:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding BackupSettings.SmbPath}" Margin="0,0,20,10"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="SMB Username:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding BackupSettings.SmbUsername}" Margin="0,0,20,10"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Operations -->
            <Border BorderBrush="Gray" BorderThickness="1" Padding="15" CornerRadius="5">
                <StackPanel Spacing="15">
                    <TextBlock Text="Operations" FontSize="16" FontWeight="Bold"/>
                    
                    <StackPanel Orientation="Horizontal" Spacing="15">
                        <Button Content="Backup Database" Command="{Binding BackupDatabaseCommand}" 
                                IsEnabled="{Binding !IsOperationInProgress}" Padding="15,8"/>
                        <Button Content="One-Way Sync" Command="{Binding SyncDatabasesCommand}" 
                                IsEnabled="{Binding !IsOperationInProgress}" Padding="15,8"/>
                    </StackPanel>

                    <Separator/>

                    <StackPanel Spacing="10">
                        <TextBlock Text="Restore Database" FontWeight="Bold"/>
                        <Grid ColumnDefinitions="*,Auto,Auto" Margin="0,5">
                            <TextBox Grid.Column="0" Text="{Binding BackupFilePath}" 
                                     Watermark="Select backup file..." Margin="0,0,10,0"/>
                            <Button Grid.Column="1" Content="Browse" Command="{Binding BrowseBackupFileCommand}" 
                                    Margin="0,0,10,0"/>
                            <Button Grid.Column="2" Content="Restore" Command="{Binding RestoreDatabaseCommand}" 
                                    IsEnabled="{Binding !IsOperationInProgress}"/>
                        </Grid>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Progress Indicator -->
            <ProgressBar IsIndeterminate="{Binding IsOperationInProgress}" 
                         IsVisible="{Binding IsOperationInProgress}" Height="10"/>

        </StackPanel>
    </ScrollViewer>
</Window>
